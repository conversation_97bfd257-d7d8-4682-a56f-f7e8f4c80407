<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/shape_bp_muse_dialog_corner_bg"
    android:padding="@dimen/activity_horizontal_margin"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="264dp"
        android:layout_height="300dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/connect_the_device"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/half_normal_margin"
            android:indeterminate="true"
            app:indicatorColor="?colorOnSurfaceVariant"
            app:indicatorInset="2dp"
            app:indicatorSize="16dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            app:trackThickness="2dp" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_muse_list"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_menu"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="2"
                tools:listitem="@layout/item_bp_muse_list_row" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_scanning"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="260dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn_scan"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.35"
                tools:src="@drawable/ico_bt_scan_02" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="@id/img_btn_scan"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toTopOf="@id/img_btn_scan"
                tools:src="@drawable/ico_bt_scan_01" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:text="@string/searching"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_normal"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toBottomOf="@id/img_btn_scan" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>