package com.ruiheng.xmuse.ui.web.handler

import android.util.Log
import com.google.gson.Gson
import com.ruiheng.xmuse.ui.web.repository.WebCacheRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Web命令处理器
 * 负责处理来自Web页面的各种命令请求
 */
@Singleton
class WebCommandHandler @Inject constructor(
    private val webCacheRepository: WebCacheRepository
) {

    companion object {
        // 命令常量
        const val COMMAND_KEY = "command"
        const val REQUEST_TOKEN = "requestUserToken"
        const val USER_TOKEN = "userToken"
        const val TEST_COMMAND = "test"
        const val SAVE_CACHE_DATA = "saveCacheData"
        const val GET_CACHE_DATA = "getCacheData"
        const val DELETE_CACHE_DATA = "deleteCacheData"
    }

    /**
     * 执行命令
     * @param jsonString JSON命令字符串
     * @param userToken 用户Token
     * @return 执行结果的JSON字符串
     */
    fun executeCommand(jsonString: String, userToken: String?): String? {
        val jsonMap = try {
            Gson().fromJson(jsonString, Map::class.java)
        } catch (e: Exception) {
            Timber.e(e, "JSON解析失败: $jsonString")
            return createErrorResponse("JSON格式错误: ${e.message}")
        }

        val command = jsonMap[COMMAND_KEY] as? String
        Timber.d("执行命令: $command")

        return when (command) {
            REQUEST_TOKEN, USER_TOKEN -> {
                handleUserToken(userToken)
            }
            TEST_COMMAND -> {
                handleTestCommand(jsonMap)
            }
            SAVE_CACHE_DATA -> {
                handleSaveCacheData(jsonMap)
            }
            GET_CACHE_DATA -> {
                handleGetCacheData(jsonMap)
            }
            DELETE_CACHE_DATA -> {
                handleDeleteCacheData(jsonMap)
            }
            else -> {
                Timber.w("未知命令: $command")
                createErrorResponse("未知命令: $command")
            }
        }
    }

    /**
     * 处理用户Token请求
     */
    private fun handleUserToken(userToken: String?): String {
        Log.v("zcy解析",userToken.toString())
        val response = mapOf(
            "command" to "userTokenResult",
            "success" to true,
            "token" to (userToken ?: ""),
            "message" to "Token获取成功"
        )
        val jsonResponse = Gson().toJson(response)
        Timber.d("返回Token: $jsonResponse")
        return jsonResponse
    }

    /**
     * 处理测试命令
     */
    private fun handleTestCommand(jsonMap: Map<*, *>): String {
        val message = jsonMap["message"] as? String ?: "Hello Web!"
        val response = mapOf(
            "command" to "testResult",
            "success" to true,
            "message" to "Android收到消息: $message",
            "timestamp" to System.currentTimeMillis()
        )
        val jsonResponse = Gson().toJson(response)
        Timber.d("测试命令响应: $jsonResponse")
        return jsonResponse
    }

    /**
     * 处理保存缓存数据的请求
     */
    private fun handleSaveCacheData(jsonMap: Map<*, *>): String {
        try {
            val key = jsonMap["key"] as? String
            val data = jsonMap["data"] as? String

            if (key.isNullOrEmpty()) {
                return createErrorResponse("保存失败: 缺少key参数")
            }

            if (data.isNullOrEmpty()) {
                return createErrorResponse("保存失败: 缺少data参数")
            }

            val success = webCacheRepository.saveCache(key, data)
            
            val response = mapOf(
                "command" to "saveCacheDataResult",
                "success" to success,
                "key" to key,
                "message" to if (success) "数据保存成功" else "数据保存失败"
            )
            return Gson().toJson(response)

        } catch (e: Exception) {
            Timber.e(e, "保存缓存失败")
            return createErrorResponse("保存失败: ${e.message}")
        }
    }

    /**
     * 处理获取缓存数据的请求
     */
    private fun handleGetCacheData(jsonMap: Map<*, *>): String {
        try {
            val key = jsonMap["key"] as? String

            if (key.isNullOrEmpty()) {
                return createErrorResponse("获取失败: 缺少key参数")
            }

            val cachedData = webCacheRepository.getCache(key)
            val hasData = cachedData != null

            val response = mapOf(
                "command" to "getCacheDataResult",
                "success" to true,
                "key" to key,
                "data" to cachedData,
                "message" to if (hasData) "数据获取成功" else "未找到对应数据",
                "debug" to mapOf(
                    "hasData" to hasData,
                    "dataLength" to (cachedData?.length ?: 0)
                )
            )
            return Gson().toJson(response)

        } catch (e: Exception) {
            Timber.e(e, "获取缓存失败")
            return createErrorResponse("获取失败: ${e.message}")
        }
    }

    /**
     * 处理删除缓存数据的请求
     */
    private fun handleDeleteCacheData(jsonMap: Map<*, *>): String {
        try {
            val key = jsonMap["key"] as? String

            if (key.isNullOrEmpty()) {
                return createErrorResponse("删除失败: 缺少key参数")
            }

            val existingData = webCacheRepository.hasCache(key)
            val success = webCacheRepository.deleteCache(key)

            val response = mapOf(
                "command" to "deleteCacheDataResult",
                "success" to success,
                "key" to key,
                "message" to if (success) "数据删除成功" else "数据删除失败",
                "debug" to mapOf(
                    "beforeDelete" to existingData,
                    "deleteSuccess" to success
                )
            )
            return Gson().toJson(response)

        } catch (e: Exception) {
            Timber.e(e, "删除缓存失败")
            return createErrorResponse("删除失败: ${e.message}")
        }
    }

    /**
     * 创建错误响应
     */
    private fun createErrorResponse(errorMessage: String): String {
        val response = mapOf(
            "command" to "error",
            "success" to false,
            "message" to errorMessage
        )
        return Gson().toJson(response)
    }
}
