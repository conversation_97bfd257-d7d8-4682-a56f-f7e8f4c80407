package com.ruiheng.xmuse.ui.home

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ScreenUtils
import com.ruiheng.xmuse.R
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.USER_PROFILE_URL
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.ruiheng.xmuse.core.ui.ui.BaseLazyFragment
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.databinding.FragmentHomeBinding
import com.ruiheng.xmuse.ui.MainActivity
import com.ruiheng.xmuse.feature.login.LoginViewModel
import com.ruiheng.xmuse.ui.detection.DetectionActivity
import com.ruiheng.xmuse.ui.home.helper.BPHasSqcViewPage
import com.ruiheng.xmuse.ui.home.helper.BPPersonPageDialog
import com.ruiheng.xmuse.ui.web.BPWebActivity
import com.ruiheng.xmuse.wxapi.WXEntryActivity

import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeFragment : BaseLazyFragment<FragmentHomeBinding>(), IBasePageErrorView, BPHasSqcViewPage {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentHomeBinding.inflate(inflater, container, false)

    private val bluetoothConnectionViewModel by lazy {
        (requireActivity() as MainActivity).bluetoothConnectionViewModel
    }

    private val homeViewModel by lazy {
        (requireActivity() as MainActivity).homeViewModel
    }

    protected val loginViewModel: LoginViewModel by lazy {
        (requireActivity() as MainActivity).loginViewModel
    }

    private var checkMuseDialogVisible: ((Boolean) -> Boolean)? = null

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

        val callback = requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (checkMuseDialogVisible?.invoke(true) == true) {
                return@addCallback
            }

            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_HOME)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            startActivity(intent)
        }
        callback.isEnabled = true

        binding.imgBpHomeTitle.showImage(R.drawable.pic_bp_home_title)
        binding.imgBpHomeTitleLogo.showImage(R.drawable.pic_bp_xmuse_logo01)
        binding.imgBack.showImage(R.drawable.pic_bp_home_bg)
        binding.imgEegDetection.showImage(R.drawable.pic_bp_eeg)
        binding.imgMentalHealthAssessment.showImage(R.drawable.pic_bp_health)
        binding.imgBpBrainBg.showImage(R.drawable.pic_bp_brain_bg)
        binding.layoutMentalHealthAssessment.setOnClickListener(this)
        binding.layoutEegDetection.setOnClickListener(this)

        binding.viewMuseListMask.setOnClickListener(this)
        initUserInfo()

        // 初始化时获取域名配置
        homeViewModel.getXmuseDomainConfig()

        checkMuseDialogVisible =
            initSqc(this, binding.sqcLayout, museListDialogCallback = { isVisible ->
                binding.viewMuseListMask.isVisible = isVisible
            })
//        homeViewModel.startMuseDataCalculate()
        autoStoppedFlow(homeViewModel.userDataStateFlow) { userResult ->
            if (userResult.isSuccessWithData()) {
                binding.tvPerson.text = userResult.data!!.nickName
            } else {
                binding.tvPerson.text = "未登录"
            }
        }
        binding.layoutPerson.setOnClickListener(this)
    }

    override fun doLazyBusiness() {

    }

    private fun initUserInfo() {
        autoStoppedFlow(homeViewModel.userDataStateFlow) { userResult ->
            val user = userResult.data
            if (user != null) {

            } else {

            }
        }

    }

    private fun startWx(wxCode: String) {
        autoRemoveFlow(loginViewModel.bpWechatLogin(wxCode)) { result ->
            result.controlLoading(this)
            result.showResult(showSuccess = true)
            if (result.isSuccessWithData()) {

            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {

            binding.viewMuseListMask -> {
                checkMuseDialogVisible?.invoke(true)
            }

            binding.layoutPerson -> {
                if (checkUser()) {
                    BPPersonPageDialog(homeViewModel.userDataStateFlow.value.data!!) {
                        loginOut()
                    }.startShow(childFragmentManager)
                } else {
                    val intent = WXEntryActivity.startActivity(
                        requireActivity() as BaseActivity,
                        codeLogin = true
                    )
                    WXEntryActivity.onWxRespCallback = { code ->
                        if (code.isNotEmpty()) {
                            startWx(code)
                        }
                    }
                    startActivity(intent)
                }
            }

            binding.layoutMentalHealthAssessment -> {
                val userToken = RetrofitNetworkModule.provideUserToken() ?: ""
                // 使用协程获取配置URL
                lifecycleScope.launch {
                    val assessmentUrl = homeViewModel.getMentalHealthAssessmentUrl()
                    Log.v("zcy", "Mental Health Assessment URL: $assessmentUrl")
                    BPWebActivity.start(requireContext(), assessmentUrl, userToken)
                }
            }

            binding.layoutEegDetection -> {
                val intent = Intent(requireContext(), DetectionActivity::class.java)
                startActivity(intent)
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }

    private fun loginOut() {
        autoRemoveFlow(homeViewModel.quitUser()) { result ->
            result.controlLoading(this)
        }
    }

    private fun checkUser(): Boolean {
        val user = homeViewModel.userDataStateFlow.value.data
        if (user == null) {
            return false
        }
        return true
    }

    /**
     * 重写navigateToMainActivity方法，HomeFragment本身就在主页面，所以不需要跳转
     */
    override fun navigateToMainActivity() {
        // HomeFragment本身就在主页面，不执行任何跳转操作
    }

    /**
     * 重写getLogoResourceId方法，直接返回brainprint模块中的logo资源ID
     */
    override fun getLogoResourceId(): Int? {
        return R.id.img_bp_home_title_logo
    }
}