package com.ruiheng.xmuse.ui.detection

import android.annotation.SuppressLint
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import com.choosemuse.libmuse.internal.BusymindMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel.Companion
import com.ruiheng.xmuse.ui.detection.busymind.BPDetectionDataTracker
import com.ruiheng.xmuse.ui.detection.busymind.BPDetectionDataTracker.Companion.VISUALIZATION_BUSYMIND_DATA
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class BPDetectionViewModel @Inject constructor(
    val app: Application,
    private val bpDetectionDataTracker: BPDetectionDataTracker,
    private val busymindMonitor: BusymindMonitor,
    private val museDataObservableFactory: MuseDataObservableFactory,
    private val museConnector: MuseConnector
) : AndroidViewModel(app) {

    companion object {
        private const val PeriodTime = 120L
        val BAND_POWER_GRAPH = arrayOf(
            BusymindData.DELTA,
            BusymindData.ALPHA,
            BusymindData.BETA,
            BusymindData.GAMMA,
            BusymindData.THETA
        )
    }

    private var disposable = CompositeDisposable()
    private val frameRateEpochMillisClock = Observable.interval(
        PeriodTime,
        TimeUnit.MILLISECONDS
    )

    //
    private val rxCountdownUtil = RxCountdownUtil()

    private val _completedLiveData = MutableLiveData(false)
    val completedLiveData = _completedLiveData as LiveData<Boolean>

    private val _remainingTimeLiveData = MutableLiveData("00:00")
    val remainingTimeLiveData = _remainingTimeLiveData as LiveData<String>

    val cacheBusyMindData: Map<BusymindData, MutableList<Double>> =
        VISUALIZATION_BUSYMIND_DATA.associateWith {
            mutableListOf()
        }

    /**
     * 衍生值数据
     * 心率、体动、HRV系列数值 -- 用于图表更新
     */
    private val _latestBusymindValueMapLiveData =
        MutableLiveData(bpDetectionDataTracker.loadLastValueMap())
    val latestBusymindValueMapLiveData =
        _latestBusymindValueMapLiveData as LiveData<Map<BusymindData, Double>>

    /**
     * PPG 原始数据 --用于图表更新
     */
    private var lastPPGValueMap = mapOf(Ppg.AMBIENT to 0.0, Ppg.IR to 0.0, Ppg.RED to 0.0)
    private val _latestPPGValueMapLiveData =
        MutableLiveData(lastPPGValueMap)
    val latestPPGValueMapLiveData = _latestPPGValueMapLiveData as LiveData<Map<Ppg, Double>>

    private val _busymindModeLiveData = MutableLiveData(BusymindMode.CALIBRATION)
    val busymindModeLiveData = _busymindModeLiveData as LiveData<BusymindMode>

    private var busymindModeDisposable: Disposable? = null

    init {
        busymindMonitor.resetCalibration()
        busymindModeDisposable =
            busymindMonitor.busymindModeObservable
                .subscribe { busymindMode ->
                    _busymindModeLiveData.value = busymindMode
                    if (busymindMode == BusymindMode.FEEDBACK) {
                        busymindModeDisposable?.dispose()
                        startDataPicker()
                    }
                }
    }

    private fun startDataPicker() {
        bpDetectionDataTracker.startTracking()
        frameRateEpochMillisClock
            .map {
                bpDetectionDataTracker.loadLastValueMap()
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { valueMap ->
                _latestBusymindValueMapLiveData.value = valueMap
                _latestPPGValueMapLiveData.value = lastPPGValueMap
            }.let { disposable.add(it) }

        rxCountdownUtil.setListener(object : RxCountdownUtil.Listener {
            @SuppressLint("DefaultLocale")
            override fun onIntervalTick(remainingSeconds: Long) {
                val minutes = remainingSeconds / 60
                val seconds = remainingSeconds % 60
                val timeText = String.format("%02d:%02d", minutes, seconds)
                _remainingTimeLiveData.value = timeText
                cacheLastData()
            }

            override fun onFinish() {
                _completedLiveData.value = true
                bpDetectionDataTracker.stopTracking()
                disposable.clear()
            }
        })
        val muse = museConnector.getCurrentMuse() ?: return

        museDataObservableFactory.createDataObservable(muse, MuseDataPacketType.PPG)
            .observeOn(Schedulers.io()).subscribe { museDataPacket ->
                lastPPGValueMap = mapOf(
                    Ppg.AMBIENT to museDataPacket.getPpgChannelValue(Ppg.AMBIENT),
                    Ppg.RED to museDataPacket.getPpgChannelValue(Ppg.RED),
                    Ppg.IR to museDataPacket.getPpgChannelValue(Ppg.IR)
                )
            }.let { disposable.add(it) }
        rxCountdownUtil.start()
    }

    private fun cacheLastData() {
        val lastDataMap = _latestBusymindValueMapLiveData.value
        cacheBusyMindData.forEach { busymindData, cacheList ->
            val value: Double? = lastDataMap?.get(busymindData)
            if (value != null) {
                cacheList.add(value)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        bpDetectionDataTracker.stopTracking()
        rxCountdownUtil.onDestroy()
        disposable.clear()
    }
}

