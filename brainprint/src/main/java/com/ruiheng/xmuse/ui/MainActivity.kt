/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.ui

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.ToastUtils
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.databinding.ActivityMainBinding
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.SignalQualityViewModel
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel
import com.ruiheng.xmuse.feature.login.LoginViewModel
import com.ruiheng.xmuse.ui.home.HomeFragment
import com.ruiheng.xmuse.ui.home.HomeViewModel
import dagger.hilt.android.AndroidEntryPoint
import me.jessyan.autosize.internal.CustomAdapt

@AndroidEntryPoint
class MainActivity : BaseBindingActivity<ActivityMainBinding>(), CustomAdapt {
    override fun bindDataBindingView() = ActivityMainBinding.inflate(layoutInflater)

    val bluetoothConnectionViewModel: BluetoothConnectionViewModel by viewModels()
    val homeViewModel: HomeViewModel by viewModels()
    val xmuseVisualizationViewViewModel: XmuseVisualizationViewViewModel by viewModels()
    val signalQualityViewModel: SignalQualityViewModel by viewModels()

    val loginViewModel: LoginViewModel by viewModels()

    private val homeFragment by lazy {
        HomeFragment()
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        ScreenUtils.setFullScreen(this)
        BarUtils.setStatusBarVisibility(this, false)
        val fragments = supportFragmentManager.fragments
        val removeTransaction = supportFragmentManager.beginTransaction()
        fragments.forEach { fragment ->
            removeTransaction.remove(fragment)
        }
        removeTransaction.commit()

        val transaction = supportFragmentManager.beginTransaction()
        transaction.add(
            binding.fragmentContainer.id,
            homeFragment,
            homeFragment.javaClass.simpleName
        )

        transaction.show(homeFragment)
        transaction.commit()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val isUserExpired = intent.getBooleanExtra("isUserExpired", false)
        if (isUserExpired) {
            ToastUtils.showShort("账号失效，请重新登录")
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
    }

    override fun isBaseOnWidth(): Boolean = true

    override fun getSizeInDp() = 960f
}
