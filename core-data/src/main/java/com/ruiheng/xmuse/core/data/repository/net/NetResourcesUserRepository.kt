package com.ruiheng.xmuse.core.data.repository.net

import android.util.Log
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.PhoneUtils
import com.blankj.utilcode.util.Utils
import com.google.gson.JsonObject
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.WECHAT_APP_ID
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.database.dao.UserDao
import com.ruiheng.xmuse.core.database.model.ThirdPartyEntity
import com.ruiheng.xmuse.core.database.model.UserAioTokenEntity
import com.ruiheng.xmuse.core.database.model.UserEntity
import com.ruiheng.xmuse.core.database.model.asExternalModel
import com.ruiheng.xmuse.core.model.data.UserAioTokenData
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.model.data.UserThirdParty
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.network.model.user.RequestBPAioInfo
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.network.model.user.RequestUploadFile
import com.ruiheng.xmuse.core.network.model.user.RequestUserBondThird
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import com.ruiheng.xmuse.core.network.other.WxService
import com.ruiheng.xmuse.core.network.user.FileService
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_AVATAR
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_OSS_PARENT
import com.ruiheng.xmuse.core.network.user.FileService.Companion.OSS_PARENT
import com.ruiheng.xmuse.core.network.user.UserService
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesUserRepository @Inject constructor(
    private val userService: UserService,
    private val fileService: FileService,
    private val userDao: UserDao
) {
    private val iwxApi: IWXAPI = WXAPIFactory.createWXAPI(Utils.getApp(), WECHAT_APP_ID, true)
    private var wxStartDisposable: Disposable? = null

    companion object {
        const val SEND_SMS_TYPE_LOGIN = 4
        const val SEND_SMS_TYPE_FORGET_RESET = 5
        const val SEND_SMS_TYPE_RESET_PHONE = 6
        const val SEND_SMS_TYPE_DELETE_USER = 8

        const val SEND_EMAIL_SMS_TYPE_LOGIN = 1
        const val SEND_EMAIL_SMS_TYPE_FORGET_RESET = 2
        const val SEND_EMAIL_SMS_TYPE_RESET = 3
        const val SEND_EMAIL_SMS_TYPE_DELETE_USER = 4

    }

    init {
        iwxApi.registerApp(WECHAT_APP_ID)
    }

    /**
     * 是否安装微信
     */
    fun isWXAppInstalled() = iwxApi.isWXAppInstalled

    /**
     * 启动微信登录
     */
    fun startWxAuthor(): Boolean {
        wxStartDisposable?.dispose()
        if (iwxApi.isWXAppInstalled) {
            wxStartDisposable = Observable.timer(500, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    if (iwxApi.isWXAppInstalled) {
                        val request = SendAuth.Req()
                        request.scope = "snsapi_userinfo"
                        request.state = "ruiheng"
                        iwxApi.sendReq(request)
                        val topActivity = ActivityUtils.getTopActivity()
                        if (topActivity.localClassName.contains("WXEntryActivity")) {
                            topActivity.finish()
                        }
                    }
                }
            return true
        }
        return false
    }

    fun sendSMS(phone: String, smsType: Int = SEND_SMS_TYPE_LOGIN) = flow<Result<Any>> {
        emit(Result.Loading())
        val requestBody =
            generateRequestDataJson(Pair("phone", phone), Pair("smsType", smsType.toString()))
        val response = userService.sendSMS(requestBody)
        requestCatch(response, dataResponse = { true }, successCallback = { result ->
            emit(Result.Success("success"))
        })
    }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 发送邮箱登录二维码
     */
    fun sendEmailSMS(email: String, emailType: Int = SEND_EMAIL_SMS_TYPE_LOGIN) =
        flow<Result<Any>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson(
                Pair("email", email),
                Pair("emailType", emailType.toString())
            )
            val response = userService.sendEmail(requestBody)
            requestCatch(response, dataResponse = { true }, successCallback = { result ->
                emit(Result.Success("success"))
            })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 绑定手机号码：
     * 1.第三方登陆时强制绑定手机号码
     */
    fun bindPhone(phone: String, smsCode: String) = flow<Result<RequestBindPhoneResult>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(Pair("phone", phone), Pair("smsCode", smsCode))
        val response = userService.bindPhone(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                //手机号无冲突正常绑定
                if (apiUser?.isBindPhoneSuccess() == true) {
                    val userResult = apiUser.result!!
                    userDao.deleteInterimUser()
                    val cacheUser = userResult.asDaoEntity()
                    userDao.insertUserAndSelect(cacheUser)
                }
                apiUser
            },
            successCallback = { result ->
                if (result.isBindPhoneSuccess()) {
                    emit(Result.Success(result))
                } else {
                    emit(Result.Error(PetivityThrowable(), data = result))
                }
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 绑定邮箱号：
     * 1.第三方登陆时强制绑定手机号码
     */
    fun bindEmail(email: String, smsCode: String) = flow<Result<RequestBindPhoneResult>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(Pair("email", email), Pair("smsCode", smsCode))
        val response = userService.bindEmail(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                //手机号无冲突正常绑定
                if (apiUser?.isBindPhoneSuccess() == true) {
                    val userResult = apiUser.result!!
                    userDao.deleteInterimUser()
                    val cacheUser = userResult.asDaoEntity()
                    userDao.insertUserAndSelect(cacheUser)
                }
                apiUser
            },
            successCallback = { result ->
                if (result.isBindPhoneSuccess()) {
                    emit(Result.Success(result))
                } else {
                    emit(Result.Error(PetivityThrowable(), data = result))
                }
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    private val updatePhoneResponse: (suspend (RequestBindPhoneResult?) -> RequestBindPhoneResult?) =
        { result ->
            if (result?.isUpdatePhoneSuccess() == true) {
                val userResult = result.result!!
                val cacheUser = userResult.asDaoEntity()
                userDao.insertUserAndSelect(cacheUser)
            }
            result
        }

    /**
     * 绑定手机号码：
     * 当前账号换绑手机
     */
    fun updateUserPhone(currentUser: UserData, phone: String, smsCode: String) =
        flow<Result<RequestBindPhoneResult>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(Pair("phone", phone), Pair("smsCode", smsCode))
            val response = userService.bindPhone(requestBody)
            requestCatch(
                response,
                dataResponse = updatePhoneResponse,
                successCallback = { result ->
                    if (result.isUpdatePhoneSuccess()) {
                        emit(Result.Success(result))
                    } else {
                        emit(Result.Error(PetivityThrowable(), data = result))
                    }
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 绑定邮箱：
     * 当前账号换绑邮箱
     */
    fun updateUserEmail(currentUser: UserData, email: String, smsCode: String) =
        flow<Result<RequestBindPhoneResult>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(Pair("email", email), Pair("smsCode", smsCode))
            val response = userService.bindEmail(requestBody)
            requestCatch(
                response,
                dataResponse = updatePhoneResponse,
                successCallback = { result ->
                    if (result.isUpdatePhoneSuccess()) {
                        emit(Result.Success(result))
                    } else {
                        emit(Result.Error(PetivityThrowable(), data = result))
                    }
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 绑定手机号码：
     * 当前账号换绑手机
     */
    fun forceUpdatePhone(currentUser: UserData, code: String) =
        flow<Result<RequestBindPhoneResult>> {
            emit(Result.Loading())
            val response = userService.forceUpdatePhone(generateRequestSingleDataJson(code))
            requestCatch(
                response,
                dataResponse = updatePhoneResponse,
                successCallback = { result ->
                    if (result.isUpdatePhoneSuccess()) {
                        emit(Result.Success(result))
                    } else {
                        emit(Result.Error(PetivityThrowable(), data = result))
                    }
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 强制绑定邮箱
     * @param code 调取 @see[bindEmail] 后，如果有冲突的话在结果中会有该 code
     */
    fun forceUpdateEmail(currentUser: UserData, code: String) =
        flow<Result<RequestBindPhoneResult>> {
            emit(Result.Loading())
            val response = userService.forceUpdateEmail(generateRequestSingleDataJson(code))
            requestCatch(
                response,
                dataResponse = updatePhoneResponse,
                successCallback = { result ->
                    if (result.isUpdatePhoneSuccess()) {
                        emit(Result.Success(result))
                    } else {
                        emit(Result.Error(PetivityThrowable(), data = result))
                    }
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 手机号 验证码登录
     * @param phone 手机号
     * @param smsCode 验证码
     *
     * TODO 需要加入区号选择功能
     */
    fun phoneLogin(phone: String, smsCode: String) = flow<Result<UserData>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("phone", phone),
            Pair("smsCode", smsCode),
            Pair("loginMethod", "1")
        )
        val response = userService.phoneLogin(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                val entity = apiUser?.asDaoEntity()
                if (entity != null) {
                    // 优先保存 JWT token，如果没有则保存普通 token
                    val jwtToken = apiUser.aioToken?.jwt_token
                    val normalToken = apiUser.token
                    Timber.d("PhoneLogin - Normal token: $normalToken")
                    Timber.d("PhoneLogin - JWT token: $jwtToken")
                    if (!jwtToken.isNullOrEmpty()) {
                        Timber.d("PhoneLogin - Saving JWT token: $jwtToken")
                        RetrofitNetworkModule.saveUserToken(jwtToken)
                    } else {
                        Timber.d("PhoneLogin - JWT token is null or empty, saving normal token: $normalToken")
                        RetrofitNetworkModule.saveUserToken(apiUser)
                    }
                    userDao.insertUserAndSelect(entity)
                }
                entity?.asExternalModel()
            },
            successCallback = { user ->
                emit(Result.Success(user))

            })
    }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 邮箱验证码登录
     * @param email 邮箱地址
     * @param smsCode 验证码
     */
    fun emailLogin(email: String, smsCode: String) = flow<Result<UserData>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("email", email),
            Pair("smsCode", smsCode),
            Pair("loginMethod", "1")
        )
        val response = userService.emailLogin(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                val entity = apiUser?.asDaoEntity()
                if (entity != null) {
                    // 优先保存 JWT token，如果没有则保存普通 token
                    val jwtToken = apiUser.aioToken?.jwt_token
                    val normalToken = apiUser.token
                    Timber.d("EmailLogin - Normal token: $normalToken")
                    Timber.d("EmailLogin - JWT token: $jwtToken")
                    if (!jwtToken.isNullOrEmpty()) {
                        Timber.d("EmailLogin - Saving JWT token: $jwtToken")
                        RetrofitNetworkModule.saveUserToken(jwtToken)
                    } else {
                        Timber.d("EmailLogin - JWT token is null or empty, saving normal token: $normalToken")
                        RetrofitNetworkModule.saveUserToken(apiUser)
                    }
                    userDao.insertUserAndSelect(entity)
                }
                entity?.asExternalModel()
            },
            successCallback = { user ->
                emit(Result.Success(user))

            })
    }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 手机 账号/密码登录
     * @param account 邮箱账号
     * @param password 邮箱密码
     * @param loginMethod 1:验证码登录 2:账号密码登录
     */
    fun appLogin(account: String, password: String) = flow<Result<UserData>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("phone", account),
            Pair("password", password),
            Pair("loginMethod", "2")
        )
        val response = userService.appLogin(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                val entity = apiUser?.asDaoEntity()
                if (entity != null) {
                    // 优先保存 JWT token，如果没有则保存普通 token
                    val jwtToken = apiUser.aioToken?.jwt_token
                    val normalToken = apiUser.token
                    Timber.d("AppLogin - Normal token: $normalToken")
                    Timber.d("AppLogin - JWT token: $jwtToken")
                    if (!jwtToken.isNullOrEmpty()) {
                        Timber.d("AppLogin - Saving JWT token: $jwtToken")
                        RetrofitNetworkModule.saveUserToken(jwtToken)
                    } else {
                        Timber.d("AppLogin - JWT token is null or empty, saving normal token: $normalToken")
                        RetrofitNetworkModule.saveUserToken(apiUser)
                    }
                    userDao.insertUserAndSelect(entity)
                }
                entity?.asExternalModel()
            },
            successCallback = { user ->
                emit(Result.Success(user))

            })
    }.globalCatch().flowOn(Dispatchers.IO)


    /**
     * 邮箱 账号/密码登录
     * @param account 邮箱账号
     * @param password 邮箱密码
     * @param loginMethod 1:验证码登录 2:账号密码登录
     */
    fun appLoginByEmailAccount(account: String, password: String) = flow<Result<UserData>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("email", account),
            Pair("password", password),
            Pair("loginMethod", "2")
        )
        val response = userService.emailLogin(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                val entity = apiUser?.asDaoEntity()
                if (entity != null) {
                    RetrofitNetworkModule.saveUserToken(apiUser)
                    userDao.insertUserAndSelect(entity)
                }
                entity?.asExternalModel()
            },
            successCallback = { user ->
                emit(Result.Success(user))

            })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun selectUser(uid: String, token: String) = flow<Result<Boolean>> {
        emit(Result.Loading())
        userDao.updateUserSelected(uid)
        RetrofitNetworkModule.saveUserToken(token)
        emit(Result.Success(true))
    }.globalCatch().flowOn(Dispatchers.IO)

    fun bpWechatLogin(code: String) =
        flow<Result<UserData>> {
            emit(Result.Loading())
            val mac = com.ruiheng.xmuse.core.data.util.DeviceUtils.getUniqueId(Utils.getApp())
            val id = 100808008
            val contentJson = JsonObject()
            contentJson.addProperty("tlClientId", id)
            contentJson.addProperty("macAddr", mac)
            contentJson.addProperty("code", code)
            contentJson.addProperty("thirdType", UserDataRepository.ThirdPartyLoginType.WeChat.requestType)
            val data = JsonObject()
            data.add("data", contentJson)
            val requestBody = data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
            val response = userService.thirdLogin(requestBody)
            requestCatch(
                response,
                dataResponse = { apiUser ->
                    Timber.d("!!!!!!!!!!!BPLoginResult:${apiUser.toString()}")
                    val entity = apiUser?.asDaoEntity()
                    if (entity != null) {
                        // 保存 bpAioToken 中的 jwt_token 而不是普通的 token
                        val jwtToken = apiUser.aioToken?.jwt_token
                        if (!jwtToken.isNullOrEmpty()) {
                            RetrofitNetworkModule.saveUserToken(jwtToken)
                        }
                        userDao.insertUserAndSelect(entity)
                    }
                    entity?.asExternalModel()
                },
                successCallback = { user ->
                    emit(Result.Success(user))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun thirdLogin(code: String, thirdType: UserDataRepository.ThirdPartyLoginType) =
        flow<Result<UserData>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson(
                Pair("code", code),
                Pair("thirdType", thirdType.requestType.toString())
            )
            val response = userService.thirdLogin(requestBody)
            requestCatch(
                response,
                dataResponse = { apiUser ->
                    val entity = apiUser?.asDaoEntity()
                    if (entity != null) {
                        RetrofitNetworkModule.saveUserToken(apiUser)
                        userDao.insertUserAndSelect(entity)
                    }
                    entity?.asExternalModel()
                },
                successCallback = { user ->
                    emit(Result.Success(user))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun oneKeyLoginRequest(token: String) = flow<Result<UserData>> {
        emit(Result.Loading())
        val requestBody =
            generateRequestDataJson(Pair("phoneToken", token), Pair("loginMethod", "0"))
        val response = userService.oneKeyLogin(requestBody)
        requestCatch(
            response,
            dataResponse = { apiUser ->
                val entity = apiUser?.asDaoEntity()
                if (entity != null) {
                    // 优先保存 JWT token，如果没有则保存普通 token
                    val jwtToken = apiUser.aioToken?.jwt_token
                    val normalToken = apiUser.token
                    Timber.d("OneKeyLogin - Normal token: $normalToken")
                    Timber.d("OneKeyLogin - JWT token: $jwtToken")
                    if (!jwtToken.isNullOrEmpty()) {
                        Timber.d("OneKeyLogin - Saving JWT token: $jwtToken")
                        RetrofitNetworkModule.saveUserToken(jwtToken)
                    } else {
                        Timber.d("OneKeyLogin - JWT token is null or empty, saving normal token: $normalToken")
                        RetrofitNetworkModule.saveUserToken(apiUser)
                    }
                    userDao.insertUserAndSelect(entity)
                }
                entity?.asExternalModel()
            },
            successCallback = { user ->
                emit(Result.Success(user))

            })
    }.globalCatch()

    fun configurePassword(password: String, againPassword: String) = flow<Result<Boolean>> {
        emit(Result.Loading())
        val requestBody =
            generateRequestDataJson(
                Pair("password", password),
                Pair("againPassword", againPassword)
            )
        val response = userService.configurePassword(requestBody)
        requestCatch(
            response,
            dataResponse = { result ->
                true
            },
            successCallback = { result ->
                emit(Result.Success(result))
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun updatePassword(oldPawd: String, password: String, againPassword: String) =
        flow<Result<Boolean>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("oldPassword", oldPawd),
                    Pair("password", password),
                    Pair("againPassword", againPassword)
                )
            val response = userService.updatePassword(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    true
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 手机号账号重置密码
     */
    fun resetPassword(account: String, smsCode: String, password: String, againPassword: String) =
        flow<Result<Boolean>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("phone", account),
                    Pair("smsCode", smsCode),
                    Pair("password", password),
                    Pair("againPassword", againPassword)
                )
            val response = userService.resetPassword(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    true
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 邮箱账号重置密码
     */
    fun emailResetPassword(
        email: String,
        smsCode: String,
        password: String,
        againPassword: String
    ) =
        flow<Result<Boolean>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("email", email),
                    Pair("smsCode", smsCode),
                    Pair("password", password),
                    Pair("againPassword", againPassword)
                )
            val response = userService.emailResetPassword(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    true
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun userLogout() =
        flow<Result<Boolean>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson()
            val response = userService.userLogout(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    userDao.deleteCurrentUsers()
                    RetrofitNetworkModule.saveUserToken("")
                    true
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                },
                onErrorCallback = {
                    userDao.deleteCurrentUsers()
                    RetrofitNetworkModule.saveUserToken("")
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun userDelete(codeType: Int, smsCode: String) =
        flow<Result<Boolean>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("codeType", codeType.toString()),
                    Pair("smsCode", smsCode)
                )
            val response = userService.accountTermination(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    userDao.deleteCurrentUsers()
                    RetrofitNetworkModule.saveUserToken("")
                    true
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                },
                onErrorCallback = {
                    userDao.deleteCurrentUsers()
                    RetrofitNetworkModule.saveUserToken("")
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun uploadImageFile(filePath: String) = flow<Result<RequestUploadFile>> {
        emit(Result.Loading())
        val file = File(filePath)
        val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
        val body = MultipartBody.Part.createFormData("file", file.name, requestFile)
//        val builder = MultipartBody.Builder()
//        if (file.exists()) {
//            val requestBody = file.asRequestBody()
//            builder.addFormDataPart("file", URLEncoder.encode(file.name, "utf-8"), requestBody)
//        }
        val response = fileService.uploadFile(
            body,
            "${OSS_PARENT}/${APP_OSS_PARENT}/${APP_AVATAR}/".toRequestBody()
        )
        requestCatch(response, successCallback = { result ->
            emit(Result.Success(result))
        })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun updateUserInfo(
        nickName: String? = null,
        headPortrait: String? = null,
        gender: Int? = null,
        birthdayYear: Int? = null
    ) =
        flow<Result<UserData>> {
            emit(Result.Loading())
            val params = mutableListOf<Pair<String, String>>()
            if (!nickName.isNullOrEmpty()) {
                params.add(Pair("nickName", nickName))
            }
            if (!headPortrait.isNullOrEmpty()) {
                params.add(Pair("headPortrait", headPortrait))
            }
            if (gender != null) {
                params.add(Pair("gender", gender.toString()))
            }
            if (birthdayYear != null) {
                params.add(Pair("birthday", "${birthdayYear}-01-01"))
            }
            val requestBody = generateRequestDataJson(*(params.toTypedArray()))
            val response = userService.updateUserInfo(requestBody)
            requestCatch(
                response,
                dataResponse = { apiUser ->
                    val entity = apiUser?.asDaoEntity()
                    if (entity != null) {
                        userDao.insertUserAndSelect(entity)
                    }
                    entity?.asExternalModel()
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun loadThirdPartyList() = flow<Result<List<UserThirdParty>>> {
        emit(Result.Loading())
        val response = userService.loadThirdPartyList(generateRequestDataJson())
        requestCatch(
            response,
            dataResponse = { result ->
                val entityList = result?.map { it.asDaoEntity() } ?: listOf()
                userDao.updateThirdPartyList(entityList)
                entityList.map { it.asExternalModel() }
            },
            successCallback = { result ->
                emit(Result.Success(result))
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun bindThirdParty(code: String, thirdType: Int) =
        flow<Result<RequestBindThirdPartyResult>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("code", code),
                    Pair("thirdType", thirdType.toString())
                )
            val response = userService.bindThirdAccount(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    result?.thirdPartyType = thirdType
                    if (result?.isThirdPartyBindSuccess() == true) {
                        val entityList = result.bindRelation?.map { it.asDaoEntity() } ?: listOf()
                        userDao.updateThirdPartyList(entityList)
                    }
                    result
                },
                successCallback = { result ->
                    if (result.isThirdPartyBindSuccess()) {
                        emit(Result.Success(result))
                    } else {
                        emit(Result.Error(PetivityThrowable(), data = result))
                    }
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun unbindThirdPartyList(unionId: String, thirdType: Int) =
        flow<Result<List<UserThirdParty>>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(
                    Pair("unionId", unionId),
                    Pair("thirdType", thirdType.toString())
                )
            val response = userService.unbindThirdAccount(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    val entityList = result?.map { it.asDaoEntity() } ?: listOf()
                    userDao.updateThirdPartyList(entityList)
                    entityList.map { it.asExternalModel() }
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun forceUpdateThirdParty(code: String, thirdType: Int) =
        flow<Result<List<UserThirdParty>>> {
            emit(Result.Loading())
            val requestBody = generateRequestSingleDataJson(code)
            val response = userService.forceUpdateThirdParty(requestBody)
            requestCatch(
                response,
                dataResponse = { result ->
                    val entityList = result?.map { it.asDaoEntity() } ?: listOf()
                    userDao.updateThirdPartyList(entityList)
                    entityList.map { it.asExternalModel() }
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun bindDevice(
        name: String,
        sn: String,
        firmwareVersion: String,
        model: String,
        bluetoothMac: String
    ) = flow<Result<RequestDeviceBindResult>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("name", name),
            Pair("sn", sn),
            Pair("firmwareVersion", firmwareVersion),
            Pair("model", model),
            Pair("bluetoothMac", bluetoothMac)
        )
        val response = userService.bindDevice(requestBody)
        requestCatch(
            response,
            successCallback = { result ->
                emit(Result.Success(result))
            })
    }.globalCatch().flowOn(Dispatchers.IO)
}

fun generateRequestDataJson(vararg keyValues: Pair<String, String>): RequestBody {
    val contentJson = JsonObject()
    keyValues.forEach { keyValue ->
        val key = keyValue.first
        val value = keyValue.second
        contentJson.addProperty(key, value)
    }
    val data = JsonObject()
    data.add("data", contentJson)
    return data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}

fun generateRequestSingleDataJson(dataString: String): RequestBody {
    val data = JsonObject()
    data.addProperty("data", dataString)
    return data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}


fun RequestUserResult.asDaoEntity() = UserEntity(
    uid = id,
    nickName = nickName,
    phone = phone,
    headPortrait = headPortrait,
    token = token,
    judgeInputPassword = judgeInputPassword,
    judgeInputPhone = judgeInputPhone,
    selected = true,
    deviceId = deviceId,
    email = email,
    gender = gender,
    birthday = birthday,
    judgeInputBind = judgeInputBind,
    aioToken = aioToken?.asDaoEntity()
)

fun RequestBPAioInfo.asDaoEntity() = UserAioTokenEntity(code, jwt_token, message, uid)

fun RequestUserBondThird.asDaoEntity() = ThirdPartyEntity(
    uid = id,
    nickName = nickName,
    headPortrait = headPortrait,
    thirdType = thirdType,
    unionId = unionId,
    userId = userId
)
